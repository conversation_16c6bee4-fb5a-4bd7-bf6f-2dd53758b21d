from bs4 import BeautifulSoup
from tqdm import trange
import util.RequestUtil as Request
import util.ToolsUtil as Tools
import util.ConfigUtil as Config
import pandas as pd
import signal

import threading
import queue
def worker(i, q):
    while not q.empty():
        offset = q.get()
        a = Request.get_message_my(offset, 10)
        print(f"Thread {threading.current_thread().name} processed index {i}")
        q.task_done()
def main():
    num_threads = 20  # 设定线程数量
    max_index = 1900000  # 假设你需要处理到这个索引值

    # 创建一个队列，并将所有的索引值放入队列
    work_queue = queue.Queue()
    i=0
    while True:
        work_queue.put(i * 10)  # 因为offset是以10为增量的
        i=i+1
        if i == 100000:
            break


    #for i in range(max_index):


    # 创建并启动线程
    threads = []
    for i in range(num_threads):
        t = threading.Thread(target=worker, args=(i, work_queue))
        t.start()
        threads.append(t)

    # 等待所有线程完成
    work_queue.join()

    # 确保所有线程都已完成
    for t in threads:
        if t.is_alive():
            t.join()
# 信号处理函数
def signal_handler(signal, frame):
    # 在手动结束程序时保存已有的数据
    if len(texts) > 0:
        save_data()
    exit(0)


def save_data():
    df = pd.DataFrame(texts, columns=['时间', '内容'])
    df.to_excel(Config.result_path + Request.uin + '.xlsx', index=False)
    print('导出成功，请查看 ' + Config.result_path + Request.uin + '.xlsx')


if __name__ == '__main__':
    try:
        user_info = Request.get_login_user_info()
        user_nickname = user_info[Request.uin][6]
        print(f"用户<{Request.uin}>,<{user_nickname}>登录成功")
    except Exception as e:
        print(f"登录失败:请重新登录,错误信息:{str(e)}")
        exit(0)
    texts = []

    try:

        i=0
        try:

            while True:
                main()
                a = Request.get_message_my(i * 10, 10)
                i = i + 1

                print(i)





        except Exception as e:
            print(f"发生异常: {str(e)}")





    except Exception as e:
        print(f"发生异常: {str(e)}")
