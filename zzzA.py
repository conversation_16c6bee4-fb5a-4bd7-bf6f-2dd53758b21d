import pymysql
import requests

# 数据库配置信息
db_config = {
    'host': '***********',
    'port': 3306,
    'user': 'cm',
    'password': 'pRAaXP5YmnBL8EWH',
    'database': 'cm',
    'charset': 'utf8mb4'
}


# 从数据库中读取所有cdKey
def get_all_cd_keys_from_db():
    try:
        # 建立数据库连接
        connection = pymysql.connect(**db_config)

        try:
            with connection.cursor() as cursor:
                # 查询所有cdKey
                sql = "SELECT cm FROM cmz"
                cursor.execute(sql)
                results = cursor.fetchall()

                # 将结果转换为列表
                cd_keys = [row[0] for row in results]
                return cd_keys
        finally:
            # 关闭连接
            connection.close()

    except Exception as e:
        print(f"发生错误: {e}")
        return []


# 发送HTTP请求并检查响应
def send_request(cd_key):
    url = "http://zly.sit360.cn/api/api.php/check"
    params = {
        'cdKey': cd_key,
        'fun': 'check'
    }

    try:
        # 发送GET请求
        response = requests.get(url, params=params)

        # 检查请求是否成功
        if response.status_code == 200:
            # 解码响应内容
            decoded_response = response.text.encode().decode('unicode_escape')

            # 检查响应内容是否包含“未激活”
            if "未激活" not in decoded_response:
                print(f"cdKey: {cd_key} 不包含未激活，将删除")
                delete_cd_key(cd_key)
            else:
                print(f"cdKey: {cd_key} 包含未激活，保留")
        else:
            print(f"cdKey: {cd_key} 请求失败，状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        # 处理请求异常
        print(f"cdKey: {cd_key} 请求过程中发生错误: {e}")


# 删除指定的cdKey
def delete_cd_key(cd_key):
    try:
        # 建立数据库连接
        connection = pymysql.connect(**db_config)

        try:
            with connection.cursor() as cursor:
                # 删除指定的cdKey
                sql = "DELETE FROM cmz WHERE cm = %s"
                cursor.execute(sql, (cd_key,))
                connection.commit()
                print(f"cdKey: {cd_key} 已删除")
        finally:
            # 关闭连接
            connection.close()

    except Exception as e:
        print(f"发生错误: {e}")


# 主函数
def main():
    cd_keys = get_all_cd_keys_from_db()
    if cd_keys:
        for cd_key in cd_keys:
            send_request(cd_key)
    else:
        print("未能从数据库中获取任何cdKey")


if __name__ == "__main__":
    main()