import requests
import random
from time import sleep

# 假设我们有以下代理列表
proxies = [
    'http://123.45.67.89:8080',
    'http://98.76.54.32:8080',
    # 添加更多代理...
]

url = 'http://zly.sit360.cn/api/api.php?fun=receiveCode&userId=106'


def fetch_with_proxy(url, proxies):
    # 随机选择一个代理
    proxy = random.choice(proxies)

    try:
        # 使用选定的代理发送请求
        response = requests.get(url, proxies={'http': proxy, 'https': proxy}, timeout=5)
        if response.status_code == 200:
            print(f'Successfully fetched with proxy {proxy}')
            return response.json()  # 返回JSON响应
        else:
            print(f'Failed to fetch with proxy {proxy}, status code: {response.status_code}')
            return None
    except Exception as e:
        print(f'Error occurred with proxy {proxy}: {str(e)}')
        return None


if __name__ == '__main__':
    while True:
        result = fetch_with_proxy(url, proxies)
        if result:
            print('Response data:', result['data'])

        # 每次请求后等待一段时间再进行下一次尝试
        sleep(10)  # 休眠10秒