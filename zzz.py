import requests
import json
import time
import concurrent.futures
import pymysql
from dbutils.pooled_db import PooledDB
from datetime import datetime

# 隧道域名:端口号
tunnel = "m333.kdltps.com:15818"

# 用户名密码方式
username = "t12883040221179"
password = "7r7m2nqu"
proxies = {
    "http": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": username, "pwd": password, "proxy": tunnel},
    "https": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": username, "pwd": password, "proxy": tunnel}
}

# 要访问的目标网页
target_url = "http://zly.sit360.cn/api/api.php?fun=receiveCode&userId=106"

# MySQL数据库配置
db_config = {
    'host': '***********',
    'port': 3306,
    'user': 'cm',
    'password': 'pRAaXP5YmnBL8EWH',
    'database': 'cm',
    'charset': 'utf8mb4'
}

# 创建数据库连接池
pool = PooledDB(pymysql, 30, **db_config)


def fetch_data():
    try:
        # 使用隧道域名发送请求
        response = requests.get(target_url, proxies=proxies, timeout=10)

        # 获取页面内容
        if response.status_code == 200:
            # 解析响应中的数据
            data = response.json().get('data', '')
            return data
        else:
            print(f"Failed to fetch data, status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None


def save_to_mysql(data):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        # 从连接池获取连接
        with pool.connection() as connection:
            with connection.cursor() as cursor:
                # 插入数据
                sql = "INSERT INTO cmz (cm) VALUES (%s)"
                cursor.execute(sql, (data,))
                connection.commit()
        print(f"{current_time} 卡密获取成功: {data}")
    except pymysql.IntegrityError:
        print(f"{current_time} 卡密已经存在: {data}")
    except Exception as e:
        print(f"{current_time} 数据库操作失败: {str(e)}")


def fetch_and_save_data():
    data = fetch_data()
    if data:
        save_to_mysql(data)


def main():
    # 设置线程池的最大线程数
    max_threads = 30

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        while True:
            # 提交任务到线程池
            futures = [executor.submit(fetch_and_save_data) for _ in range(max_threads)]

            # 等待所有任务完成
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"任务执行失败: {str(e)}")

            # 每次循环后等待一段时间
            time.sleep(1)


if __name__ == '__main__':
    main()