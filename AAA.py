import requests

url = "https://user.qzone.qq.com/proxy/domain/taotao.qq.com/cgi-bin/emotion_cgi_msglist_v6"

querystring = {"uin":"3058333641","ftype":"0","sort":"0","pos":"0","num":"20","replynum":"100","g_tk":["1917925480","1917925480"],"callback":"_preloadCallback","code_version":"1","format":"jsonp","need_private_comment":"1"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "Cookie": "fopenid=A6A7A02DAE403FB7DDA0CBD71C1D4787; it_c=0; openid=A6A7A02DAE403FB7DDA0CBD71C1D4787; pgv_pvid=6298242730; ptcz=c0748addc790863b8fd127b01e01617086aa1187b71fb8580bc6fc8ed7fbcc0e; RK=GBdBOpvnm1; token=2E793B59513411B14B91751BA6F35D62; __Q_w_s__QZN_TodoMsgCnt=1; __Q_w_s_hat_seed=1; QZ_FE_WEBP_SUPPORT=1; randomSeed=290912; pac_uid=0_6bfdiTAFTp766; _qimei_uuid42=1891a163b26100880697ce890e176fab2d9a702308; suid=user_0_6bfdiTAFTp766; _qimei_fingerprint=ed81f08c60952cdfedc7e3543eb1e9f2; _qimei_q32=60f9008d36eb9e8859b69c4c7d85ff8f; _qimei_q36=eff4d8c6b05bd05b7a3bb493300012218201; _qimei_h38=fded350fb989cc240f06bb6a0200000b618605; current-city-name=suz; ptui_loginuin=382854425; _qpsvr_localtk=0.29306610995375104; pgv_info=ssid=s8954704360; Loading=Yes; 3058333641_todaycount=4; 3058333641_totalcount=47339; uin=o3058333641; skey=@HQQrn79p6; p_uin=o3058333641; pt4_token=EAAZ9WA6E94z9avninfmklBLQ5RuxdIAgug5flAChmU_; p_skey=oVUt6LV2h-tIkfBtvV1lKCO1kfPZT-rGCxfRZGyiRxg_; qz_screen=1536x864; cpu_performance_v8=45 ",
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "User-Agent": "PostmanRuntime-ApipostRuntime/1.1.0",
    "Connection": "keep-alive",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)