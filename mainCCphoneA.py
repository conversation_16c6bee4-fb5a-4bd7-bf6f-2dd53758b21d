from bs4 import BeautifulSoup
from tqdm import trange
import util.RequestUtil as Request
import util.ToolsUtil as Tools
import util.ConfigUtil as Config
import pandas as pd
import signal
from concurrent.futures import ThreadPoolExecutor, as_completed

# 信号处理函数
def signal_handler(signal, frame):
    # 在手动结束程序时保存已有的数据
    if len(texts) > 0:
        save_data()
    exit(0)

def save_data():
    df = pd.DataFrame(texts, columns=['时间', '内容'])
    df.to_excel(Config.result_path + Request.uin + '.xlsx', index=False)
    print('导出成功，请查看 ' + Config.result_path + Request.uin + '.xlsx')

def fetch_messages(start_index, batch_size):
    try:
        messages = Request.get_messageOO(start_index, batch_size)
        for message in messages:
            print(message)
            # Assuming the message is a tuple or list with time and content.
            texts.append((message['time'], message['content']))
    except Exception as e:
        print(f"获取消息失败: {str(e)}")

if __name__ == '__main__':
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    texts = []

    try:
        user_info = Request.get_login_user_info()
        user_nickname = user_info[Request.uin][6]
        print(f"用户<{Request.uin}>,<{user_nickname}>登录成功")
    except Exception as e:
        print(f"登录失败:请重新登录,错误信息:{str(e)}")
        exit(1)

    # Define the number of threads and the batch size per thread
    num_threads = 5  # You can adjust this number based on your requirements
    batch_size = 20

    # Using ThreadPoolExecutor to manage a pool of threads
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = {executor.submit(fetch_messages, i * batch_size, batch_size): i for i in range(num_threads)}
        for future in as_completed(futures):
            try:
                future.result()  # This will raise any exceptions that occurred during the execution
            except Exception as exc:
                print(f"Generated an exception: {exc}")

    # Save data after all threads have completed their tasks
    if texts:
        save_data()