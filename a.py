from flask import Flask, request, jsonify
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

app = Flask(__name__)


@app.route('/visit', methods=['POST'])
def visit_url():
    # 检查请求头是否为application/json
    if request.content_type != 'application/json':
        return jsonify({'error': 'Content-Type must be application/json'}), 415

    data = request.json
    url = data.get('url')

    if not url:
        return jsonify({'error': 'No URL provided'}), 400

    # 设置Chrome选项以启用无头模式
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--disable-gpu')

    # 创建WebDriver对象
    driver = webdriver.Chrome(options=chrome_options)

    try:
        # 访问提供的URL
        driver.get(url)

        # 这里可以添加更多操作，比如抓取页面数据等
        title = driver.title

        return jsonify({'status': 'success', 'title': title}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        # 关闭浏览器
        driver.quit()


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=4006, debug=True)