import requests

url = "https://h5.qzone.qq.com/proxy/domain/m.qzone.qq.com/cgi-bin/new/del_msgb"

querystring = {"g_tk":"187562528"}
params = {
    "hostUin": 2028278492,
    "idList": id,
    "uinList": 2028278492,
    "format": "fs",
    "iNotice": "1",
    "inCharset": "utf-8",
    "outCharset": "utf-8",
    "ref": "qzone",
    "g_tk": 187562528,
    "qzreferrer": " https://user.qzone.qq.com/proxy/domain/qzonestyle.gtimg.cn/qzone/msgboard/msgbcanvas.html#page=1",
}
headers = {
    "Cookie": "uin=o2028278492; skey=@y4usGcfaC; pt2gguin=; pt2gguin=o2028278492; p_uin=o2028278492; pt4_token=Y3qVEuN4QaY3qrbEf*TReLg4Wlp0yB1O8kS6YuCzMSc_; p_skey_forbid=; p_skey=CknPz6rZiLsWohR73m7CGjdieZX59Sfm7uNlrNerjdg_;",
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "User-Agent": "PostmanRuntime-ApipostRuntime/1.1.0",
    "Connection": "keep-alive",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=params, headers=headers, params=querystring)

print(response.text)