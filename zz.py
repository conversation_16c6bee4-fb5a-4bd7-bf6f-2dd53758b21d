import tkinter as tk
from tkinter import messagebox, filedialog
import threading
import random
from concurrent.futures import ThreadPoolExecutor
import time
import tkinter.ttk as ttk  # 确保正确导入 ttk 模块
class RandomNumberGeneratorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("随机数生成器")
        self.root.geometry("900x300")
        self.root.resizable(True, True)

        # 创建输入区域
        input_frame = tk.Frame(root)
        input_frame.pack(pady=20)

        # 起始数字
        self.start_label = tk.Label(input_frame, text="开始数字:")
        self.start_label.grid(row=0, column=0, padx=10, pady=5, sticky=tk.W)
        self.start_entry = tk.Entry(input_frame, width=20)
        self.start_entry.grid(row=0, column=1, padx=10, pady=5)

        # 结束数字
        self.end_label = tk.Label(input_frame, text="结束数字:")
        self.end_label.grid(row=0, column=2, padx=10, pady=5, sticky=tk.W)
        self.end_entry = tk.Entry(input_frame, width=20)
        self.end_entry.grid(row=0, column=3, padx=10, pady=5)

        # 线程数
        self.threads_label = tk.Label(input_frame, text="线程数:")
        self.threads_label.grid(row=0, column=4, padx=10, pady=5, sticky=tk.W)
        self.threads_entry = tk.Entry(input_frame, width=10)
        self.threads_entry.grid(row=0, column=5, padx=10, pady=5)

        # 开始按钮
        self.start_button = tk.Button(input_frame, text="开始", command=self.start_generation, width=10)
        self.start_button.grid(row=0, column=6, padx=10, pady=5)

        # 停止按钮
        self.stop_button = tk.Button(input_frame, text="停止", command=self.stop_generation, width=10, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=7, padx=10, pady=5)

        # 显示已生成数字数量
        status_frame = tk.Frame(root)
        status_frame.pack(pady=20)
        self.generated_count_label = tk.Label(status_frame, text="已生成: 0")
        self.generated_count_label.pack()

        # 进度条
        self.progress_var = tk.IntVar()
        self.progress_bar = tk.ttk.Progressbar(root, variable=self.progress_var, maximum=100, mode='determinate')
        self.progress_bar.pack(pady=10)

        # 导出按钮
        export_frame = tk.Frame(root)
        export_frame.pack(pady=20)
        self.export_button = tk.Button(export_frame, text="导出", command=self.export_to_txt, width=10, state=tk.DISABLED)
        self.export_button.pack()

        # 数据存储
        self.is_generating = False
        self.total_numbers = 0
        self.generated_numbers = 0
        self.numbers = []

    def generate_numbers(self, start, end, threads):
        batch_size = 1000
        total_batches = (end - start + 1) // batch_size + 1

        with ThreadPoolExecutor(max_workers=threads) as executor:
            for batch_idx in range(total_batches):
                start_idx = start + batch_idx * batch_size
                end_idx = min(start + (batch_idx + 1) * batch_size - 1, end)
                batch = list(range(start_idx, end_idx + 1))
                random.shuffle(batch)

                for number in batch:
                    if not self.is_generating:
                        return
                    self.numbers.append(str(number))
                    self.generated_numbers += 1

                if self.generated_numbers % 1000 == 0:
                    self.root.after(0, self.update_generated_count)
                    self.root.after(0, self.update_progress)

    def start_generation(self):
        if self.is_generating:
            messagebox.showwarning("警告", "已经在生成数字，请等待完成。")
            return

        try:
            start_str = self.start_entry.get().strip()
            end_str = self.end_entry.get().strip()
            threads = int(self.threads_entry.get())

            start = int(start_str)
            end = int(end_str)

            if start > end or threads <= 0:
                raise ValueError("起始数字必须小于或等于结束数字，线程数必须大于0。")

            max_length = 12
            if len(start_str) > max_length or len(end_str) > max_length:
                raise ValueError(f"数字长度不能超过{max_length}位。")

            self.numbers = []
            self.generated_count_label.config(text="已生成: 0")
            self.is_generating = True
            self.total_numbers = end - start + 1
            self.generated_numbers = 0
            self.progress_var.set(0)

            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.export_button.config(state=tk.DISABLED)  # 禁用导出按钮

            generation_thread = threading.Thread(target=self.run_generation, args=(start, end, threads))
            generation_thread.start()
        except ValueError as e:
            messagebox.showerror("错误", str(e))

    def run_generation(self, start, end, threads):
        self.generate_numbers(start, end, threads)
        self.is_generating = False
        self.root.after(0, self.check_completion)

    def update_generated_count(self):
        self.generated_count_label.config(text=f"已生成: {self.generated_numbers}")

    def update_progress(self):
        progress = (self.generated_numbers / self.total_numbers) * 100
        self.progress_var.set(int(progress))

    def check_completion(self):
        if not self.is_generating:
            if self.generated_numbers == self.total_numbers:
                messagebox.showinfo("信息", "数据生成完成")
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                self.export_button.config(state=tk.NORMAL)  # 启用导出按钮
            else:
                self.root.after(1000, self.check_completion)

    def stop_generation(self):
        self.is_generating = False

    def export_to_txt(self):
        if not self.numbers:
            messagebox.showwarning("警告", "没有生成任何数字，无法导出。")
            return

        file_path = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt")])
        if not file_path:
            return

        with open(file_path, 'w') as f:
            for number in self.numbers:
                f.write(f"{number}\n")

        messagebox.showinfo("信息", f"数据已成功导出至 {file_path}")

if __name__ == "__main__":
    root = tk.Tk()
    app = RandomNumberGeneratorApp(root)
    root.mainloop()