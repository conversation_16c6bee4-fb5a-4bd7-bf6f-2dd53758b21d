import re
import random

from pandas.core.computation import scope
from tqdm import tqdm
import util.LoginUtil as Login
import requests
import json
from bs4 import BeautifulSoup
import util.ToolsUtil as Tools
# 登陆后获取到的cookies
cookies = Login.cookie()

# 获取g_tk
g_tk = Login.bkn(cookies.get('p_skey'))
# 获取uin
uin = re.sub(r'o0*', '', cookies.get('uin'))
# 全局header
headers = {
    'authority': 'user.qzone.qq.com',
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,'
              'application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'sec-ch-ua': '"Not A(Brand";v="99", "Microsoft Edge";v="121", "Chromium";v="121"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'none',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* '
                  'Safari/537.36 Edg/*********',
}

def get_messageO(start, count):

    params = {
        'uin': uin,

        'inCharset': 'utf-8',
        'outCharset': 'utf-8',
        'hostUin': uin,
        'notice': '0',
        'sort': '0',
        'pos': start,
        'num': count,



        'cgi_host': 'https://user.qzone.qq.com/proxy/domain/taotao.qq.com/cgi-bin/emotion_cgi_msglist_v6',
        'code_version': '1',
        'format': 'jsonp',
        'need_private_comment': '1',
        'g_tk': [
            g_tk,
            g_tk,
        ],
    }
    response = requests.get('https://user.qzone.qq.com/proxy/domain/taotao.qq.com/cgi-bin/emotion_cgi_msglist_v6',
                            params=params, cookies=cookies, headers=headers)
    temp=response.text
    print(temp)

    json_response = temp.replace("_Callback(", "").replace(");", "")
    json_response = json.loads(json_response)


    da=json_response["msglist"]

    if len(da) == 0:
        print("网络繁忙")
        return 0
    else:
        i = 0
        # 遍历打乱后的列表
        for element in da:
            print(element)
            #delSpeak()
            delSpeakPhone(element["tid"])

            i=i+1
            # 生成一个3到7之间的随机浮点数
            delay_time = random.uniform(0, 1)

            # 打印随机延迟的时间
            print(f"Delaying for {delay_time:.2f} seconds")

            # 暂停程序
            #time.sleep(delay_time)
    return response


def get_messageOO(start, count):
    url = "https://mobile.qzone.qq.com/list"

    params = {
        'qzonetoken': '05dcbe17ca00ff01f153a7064b13479a35b0f40ba7ecc5fd4c43a70ac89ed45e6685c4bcf9c491af05aee9c45dcbf91f',
        'g_tk': '1783655187',

        'format': 'json',
        'list_type': 'shuoshuo',
        'action': '0',
        'res_uin': '825608074',
        'count': '10'
    }




    response = requests.request("GET", url, cookies=cookies, headers=headers, params=params)

    print(response.text)
    temp=response.text
    json_response = temp.replace("_Callback(", "").replace(");", "")
    json_response = json.loads(json_response)


    da=json_response["data"]["vFeeds"]

    if len(da) == 0:
        print("网络繁忙")
        return 0
    else:
        i = 0
        # 遍历打乱后的列表
        for element in da:
            print(element)
            #delSpeak()
            delSpeakPhone(element["operation"]["busi_param"]["20"])

            i=i+1
            # 生成一个3到7之间的随机浮点数
            delay_time = random.uniform(0, 1)

            # 打印随机延迟的时间
            print(f"Delaying for {delay_time:.2f} seconds")

            # 暂停程序
            time.sleep(delay_time)
    return response
# 获取历史消息列表
def get_message(start, count):

    params = {
        'uin': uin,
        'hostuin': uin,
        'scope': '0',
        'filter': 'all',
        'flag': '1',#需要获取
        'refresh': '0',
        'firstGetGroup': '0',
        'mixnocache': '0',
        'scene': '0',
        'begintime': '0',
        'icServerTime': '',
        'icServerTime': '',
        'start': start,
        'count': count,

        'sidomain': 'qzonestyle.gtimg.cn',
        'useutf8': '1',
        'outputhtmlfeed': '1',
        'refer': '2',
        'r': '0.0240576971814656',
        'g_tk': [
            g_tk,
            g_tk,
        ],
    }
    response = requests.get('https://user.qzone.qq.com/proxy/domain/ic2.qzone.qq.com/cgi-bin/feeds/feeds_html_act_all',
                            params=params, cookies=cookies, headers=headers)
    print(response.text)
    html = Tools.process_old_html(response.text)
    soup = BeautifulSoup(html, 'html.parser')
    print(soup)

    da=soup.find_all(id_="msgList")

    if len(da) == 0:
        print("网络繁忙")
        return 0
    else:
        i = 0
        # 遍历打乱后的列表
        for element in da:
            print(element)
            #feedstime: '2020年12月30日 17:12'
            delSpeakP(element)
            i=i+1
            # 生成一个3到7之间的随机浮点数
            delay_time = random.uniform(0, 1)

            # 打印随机延迟的时间
            print(f"Delaying for {delay_time:.2f} seconds")

            # 暂停程序
            time.sleep(delay_time)
    return response

def delSpeakP(element):

    if element == '' or len(element)==1:
        return ""
    params = {
        "opr_type:": "delugc",

        "res_type": "311",
        "qzreferrer": "https://user.qzone.qq.com/" + uin+"/main",
        "real_del": "0",
        "format": "json",
        "res_id": element

    }
    print(params)
    url = 'https://mobile.qzone.qq.com/operation/operation_add?qzonetoken=19d7bc1b6c423249eee4ebad0878f0e9c57989058a4255d36bc6672fcf9b4940af70a45c3635f2231e790375ec410dbb586a3b1742fec0879f2914a2aabbf335edb38ccc562b51b1c4&g_tk={}'.format(g_tk)


    response = requests.post(url,data=params, cookies=cookies, headers=headers)
    print(response.text)
def delSpeak(element):

    if element == '' or len(element)==1:
        return ""
    params = {
        "hostuin": uin,
        "t1_source": "1",
        "code_version": "1",
        "qzreferrer": "https://user.qzone.qq.com/" + uin+"/main",
        "grz": "0.60101576971814656",
        "format": "fs",
        "tid": element

    }
    url = 'https://user.qzone.qq.com/proxy/domain/taotao.qzone.qq.com/cgi-bin/emotion_cgi_delete_v6?&g_tk={}'.format(g_tk)


    response = requests.post(url,data=params, cookies=cookies, headers=headers)
    print(response.text)

def delSpeakPhone(element):

        if element == '' or len(element) == 1:
            return ""
        params = {
	"opr_type": "delugc",
	"format": "json",
	"res_uin": uin,
	"real_del": "0",
	"res_id": element,
	"res_type": "311"
}
        url = 'https://mobile.qzone.qq.com/operation/operation_add?qzonetoken=afd787e744fdb41e58ccf47a487a66810a6541e46f60e28ab53077feb8074c75ae863b29a4e650f2a25492ff3021d8ac66f88f86397a6f575f0a388ac5f6ca8dcce00a329c07e71e&g_tk={}'.format(
            g_tk)

        response = requests.post(url, data=params, cookies=cookies, headers=headers)
        print(response.text)

        # 如果需要，打印映射以确认元素是否正确存储

def get_message_my(start, count):
    params = {
        'uin': uin,
        'begin_time': '0',
        'end_time': '0',
        'getappnotification': '1',
        'getnotifi': '1',
        'has_get_key': '0',
        'offset': start,
        'set': '1',
        'count': count,
        'useutf8': '1',
        'outputhtmlfeed': '0',
        'scope': '1',
        'grz': '0.9240576971814656',
        'format': 'jsonp',
        'g_tk': [
            g_tk,
            g_tk,
        ],
    }



    response = requests.get('https://user.qzone.qq.com/proxy/domain/ic2.qzone.qq.com/cgi-bin/feeds/feeds2_html_pav_all',
                            params=params, cookies=cookies, headers=headers)


    html = Tools.process_old_html(response.text)

    soup = BeautifulSoup(html, 'html.parser')

    da=soup.find_all('li', class_='f-single f-s-s')
    if len(da)==0:
        print(html)
        print("网络繁忙")
        return 0
    else:


        i = 0
        # 遍历打乱后的列表
        for  element in da:
            tm = element.find_all('span', class_='ui-mr8 state')
            tmm=tm[0].getText()
            print(tmm)
            if '2017' in tmm or '2016' in tmm:
                print("删除",tmm)
                delMy(response.text, i, element)





            # 假设 delMy 已经被正确定义
            i=i+1
            #break
            #continue


        # 如果需要，打印映射以确认元素是否正确存储



def get_message_myP(start, count):
    params = {
        'uin': uin,
        'begin_time': '0',
        'end_time': '0',
        'getappnotification': '1',
        'getnotifi': '1',
        'has_get_key': '0',
        'offset': start,
        'set': '1',
        'count': count,
        'useutf8': '1',
        'outputhtmlfeed': '0',
        'scope': '1',
        'grz': '0.9240576971814656',
        'format': 'jsonp',
        'g_tk': [
            g_tk,
            g_tk,
        ],
    }



    response = requests.get('https://user.qzone.qq.com/proxy/domain/ic2.qzone.qq.com/cgi-bin/feeds/feeds2_html_pav_all',
                            params=params, cookies=cookies, headers=headers)

    print(response.text)
    html = Tools.process_old_html(response.text)

    soup = BeautifulSoup(html, 'html.parser')

    da=soup.find_all('li', class_='f-single f-s-s')
    if len(da)==0:
        print(html)
        print("网络繁忙")
        return 0
    else:


        i = 0
        # 遍历打乱后的列表
        for  element in da:
            tm = element.find_all('span', class_='ui-mr8 state')
            tmm=tm[0].getText()
            print(tmm)
            if '2017' in tmm or '2016' in tmm:
                print("删除",tmm)
                delMy(response.text, i, element)





            # 假设 delMy 已经被正确定义
            i=i+1
            #break
            #continue


        # 如果需要，打印映射以确认元素是否正确存储




def delMy(text,i,element):





    pattern = r'data-topicid=\"(.*?)\"'  # 匹配id='...'
    topicid = re.findall(pattern, str(element))
    z=''
    if len(topicid)!=0:
        z=topicid[0]




    pattern = r'typeid:\'(.*?)\''  # 匹配id='...'
    typeid = re.findall(pattern, text)



    pattern = r',key:\'(.*?)\''  # 匹配key='...'，key前可有或没有,及空白字符
    key = re.findall(pattern, text)


    pattern = r',appid:\'(.*?)\''  # 匹配id='...'
    appid = re.findall(pattern, text)

    pattern = r'data-abstime=\\x22(.*?)\\x22'  # 匹配id='...'
    abstime = re.findall(pattern, text)

    pattern = r'scope:\'(.*?)\''  # 匹配id='...'
    scop = re.findall(pattern, text)




    params = {
        "qzreferrer": "https://user.qzone.qq.com/" + uin,
        "uin": uin,
        "topicId": z,
        "feedsType": typeid[i],
        "feedsFlag": scop[i],
        "feedsKey": key[i],
        "feedsAppid": appid[i],
        "feedsTime": abstime[i],
        "fupdate": 1,
        "ref": "feeds"
    }
    #print(params)
    url = 'https://user.qzone.qq.com/proxy/domain/w.qzone.qq.com/cgi-bin/feeds/feeds_delete_cgi_rtcode?g_tk={}'.format(g_tk)


    response = requests.post(url,data=params, cookies=cookies, headers=headers)
    #print(response.text)

def extract_substring(s, start_pos, end_char):
    # 从start_pos位置开始，向前查找end_char
    pos = start_pos - 1  # 起始位置减1，因为我们要向后查找
    while pos >= 0:
        if s[pos] == end_char:
            # 找到了指定字符，返回从pos+1（即end_char之后）到start_pos的子串
            return s[pos + 1:start_pos]
        pos -= 1
    # 如果没有找到指定字符，则返回空字符串
    return ""

def get_login_user_info():
    response = requests.get('https://r.qzone.qq.com/fcg-bin/cgi_get_portrait.fcg?g_tk=' + str(g_tk) + '&uins=' + uin,
                            headers=headers, cookies=cookies)
    info = response.content.decode('GBK')
    info = info.strip().lstrip('portraitCallBack(').rstrip(');')
    info = json.loads(info)
    return info

def remove_html_tags(html_string):
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_string, 'html.parser')

    # 获取文本内容
    cleaned_text = soup.get_text()

    # 进一步处理可能存在的HTML实体
    cleaned_text = re.sub(r'&[a-zA-Z0-9#]+;', '', cleaned_text)
    cleaned_text = cleaned_text.strip()  # 去除首尾空白

    return cleaned_text

def get_message_count():
    # 初始的总量范围
    lower_bound = 0
    upper_bound = 100000  # 假设最大总量为1000000
    total = upper_bound // 2  # 初始的总量为上下界的中间值
    with tqdm(desc="正在获取消息列表数量...") as pbar:
        while lower_bound <= upper_bound:
            response = get_message(total, 100)
            if "li" in response.text:
                # 请求成功，总量应该在当前总量的右侧
                lower_bound = total + 1
            else:
                # 请求失败，总量应该在当前总量的左侧
                upper_bound = total - 1
            total = (lower_bound + upper_bound) // 2  # 更新总量为新的中间值
            pbar.update(1)
    return total

import random
import time

def get_messageLY(start, count):

    params = {
        'uin': uin,
        'hostUin': uin,

        'num': count,
         'start': start,
        'hostword': '0',
        'essence': '1',
        'r': '0.3554656544664',
        'iNotice': '0',
        'inCharset': 'utf-8',
        'outCharset': 'utf-8',
        'format': 'jsonp',
        'ref': 'qzone',
        'g_tk': [
            g_tk,
            g_tk,
        ],

    }

    response = requests.get('https://user.qzone.qq.com/proxy/domain/m.qzone.qq.com/cgi-bin/new/get_msgb',
                            params=params, cookies=cookies, headers=headers)

    pattern = r"^_Callback\(|\);$"
    result = re.sub(pattern, "", response.text)
    #print(result)
    # 解析JSON数组
    parsed_array = json.loads(result)
    parsed_array=parsed_array.get("data").get("commentList")

    id=""
    pasterid=""
    # 输出解析后的数据

    i=1
    for item in parsed_array:
        id=id+","+str(item.get("id"))
        pasterid=pasterid+","+str(item.get("uin"))




    # 生成一个3到7之间的随机浮点数
    delay_time = random.uniform(2, 3)

    # 打印随机延迟的时间
    print(f"Delaying for {delay_time:.2f} seconds")

    # 暂停程序
    time.sleep(delay_time)
    delSpeakLy(id, pasterid)
    return response
#删除留言
def delSpeakLy(id,pasterid):

    params = {
        "hostUin": uin,
        "idList": id,
        "uinList": pasterid,
        "format": "fs",
        "iNotice": "1",
        "inCharset": "utf-8",
        "outCharset": "utf-8",
        "ref": "qzone",
        "g_tk": g_tk,
        "qzreferrer": " https://user.qzone.qq.com/proxy/domain/qzonestyle.gtimg.cn/qzone/msgboard/msgbcanvas.html#page=1",
    }
    url = 'https://h5.qzone.qq.com/proxy/domain/m.qzone.qq.com/cgi-bin/new/del_msgb?&g_tk={}'.format(g_tk)
    print(params)
    print("-------------------------")

    response = requests.post(url,data=params, cookies=cookies, headers=headers)
    print(response.text)
def get_messageLYPhone(start, count):

    params = {
        'uin': uin,
        'hostUin': uin,

        'num': count,
         'start': start,
        'hostword': '0',
        'essence': '1',
        'r': '0.3554656544664',
        'iNotice': '0',
        'inCharset': 'utf-8',
        'outCharset': 'utf-8',
        'format': 'jsonp',
        'ref': 'qzone',
        'g_tk': [
            g_tk,
            g_tk,
        ],

    }

    response = requests.get(' https://user.qzone.qq.com/proxy/domain/m.qzone.qq.com/cgi-bin/new/get_msgb',
                            params=params, cookies=cookies, headers=headers)

    pattern = r"^_Callback\(|\);$"
    result = re.sub(pattern, "", response.text)
    print(result)
    # 解析JSON数组
    parsed_array = json.loads(result)
    parsed_array=parsed_array.get("data").get("commentList")

    id=""
    pasterid=""
    # 输出解析后的数据

    for item in parsed_array:
        id=str(item.get("id"))
        pasterid=str(item.get("uin"))
        delSpeakLyphone(id, pasterid)
        # 生成一个3到7之间的随机浮点数
        delay_time = random.uniform(1, 2)

        # 打印随机延迟的时间
        print(f"Delaying for {delay_time:.2f} seconds")

        # 暂停程序
        time.sleep(delay_time)



    return response


def get_messageLYPhoneAndroid(start, count):
    # 生成一个0到1之间的随机浮点数
    random_number = random.random()

    # 将其限制为12位小数
    formatted_random_number = "{:.13f}".format(random_number)
    params = {
        'uin': uin,
        'hostUin': uin,

        'num': count,
         'start': start,
        'hostword': '0',
        'essence': '1',
        't': formatted_random_number,
        'iNotice': '0',
        'inCharset': 'utf-8',
        'outCharset': 'utf-8',
        'format': 'jsonp',
        'ref': 'qzone',
        'g_tk': [
            g_tk,
            g_tk,
        ],

    }

    response = requests.get(' https://user.qzone.qq.com/proxy/domain/m.qzone.qq.com/cgi-bin/new/get_msgb',
                            params=params, cookies=cookies, headers=headers)

    pattern = r"^_Callback\(|\);$"
    result = re.sub(pattern, "", response.text)
    print(result)
    # 解析JSON数组
    parsed_array = json.loads(result)
    parsed_array=parsed_array.get("data").get("commentList")

    id=""
    pasterid=""
    # 输出解析后的数据

    for item in parsed_array:
        id=str(item.get("id"))
        pasterid=str(item.get("uin"))
        delSpeakLyphoneAndroid(id, pasterid)
        # 生成一个3到7之间的随机浮点数
        delay_time = random.uniform(1, 2)

        # 打印随机延迟的时间
        print(f"Delaying for {delay_time:.2f} seconds")

        # 暂停程序
        time.sleep(delay_time)



    return response
def delSpeakLyphone(id, pasterid):
        params = {
        "opr_type": "delugc",
        "res_type": 334,
        "res_id": id,
        "real_del": "0",
        "res_uin": uin,
        "format": "json"

        }
        print(params)
        url = 'https://mobile.qzone.qq.com/operation/operation_add?qzonetoken=6e388dc78e5f3fe2ca66c2259e611b0fd909aaa3c43548559c6dcaacd4a24e98d1534fc6bcd5fec0b72dec456bab7271d4680e&g_tk={}'.format(g_tk)
        print(params)
        print("-------------------------")

        response = requests.post(url, data=params, cookies=cookies, headers=headers)
        print(response.text)
        # 如果需要，打印映射以确认元素是否正确存储

import random
def delSpeakLyphoneAndroid(id, pasterid):


    # 生成一个0到1之间的随机浮点数
    random_number = random.random()

    # 将其限制为12位小数
    formatted_random_number = "{:.13f}".format(random_number)
    params = {
        'uin': uin,
        'hostUin': uin,
        'cmd': 'delete',
        'qzonetoken': 'e6cce4af83b8ef480ccd0ffcbb80e39f516285247de52254226ef41e15af9316714aab8a5d2818a53adcd7786a8a1e9f8dba7fe8404727109d9b30d498d6e0bbf6b3057331684d1efd06',
        't': formatted_random_number,
        'cellid': id,
        'format': 'json',
        'inCharset': 'utf-8',
        'outCharset': 'utf-8',
        'g_tk':  g_tk

    }

    response = requests.get('https://h5.qzone.qq.com/message/index',
                            params=params, cookies=cookies, headers=headers)


    print(response.text)
    # 如果需要，打印映射以确认元素是否正确存储
def get_messageRz(start, count):

    params = {
        'uin': uin,
        'hostuin': uin,
        'scope': '0',
        'filter': 'all',
        'flag': '1',#需要获取
        'refresh': '0',
        'firstGetGroup': '0',
        'mixnocache': '0',
        'scene': '0',
        'begintime': '0',
        'icServerTime': '',
        'icServerTime': '',
        'start': start,
        'count': count,

        'sidomain': 'qzonestyle.gtimg.cn',
        'useutf8': '1',
        'outputhtmlfeed': '1',
        'refer': '2',
        'r': '0.0240576971814656',
        'g_tk': [
            g_tk,
            g_tk,
        ],
    }
    response = requests.get('https://user.qzone.qq.com/proxy/domain/ic2.qzone.qq.com/cgi-bin/feeds/feeds_html_act_all',
                            params=params, cookies=cookies, headers=headers)
    print(response.text)
    html = Tools.process_old_html(response.text)
    soup = BeautifulSoup(html, 'html.parser')
    print(soup)

    da=soup.find_all(id_="msgList")
#https://user.qzone.qq.com/proxy/domain/b.qzone.qq.com/cgi-bin/blognew/del_blog?&g_tk=1149040097

    if len(da) == 0:
        print("网络繁忙")
        return 0
    else:
        i = 0
        # 遍历打乱后的列表
        for element in da:
            print(element)
            #feedstime: '2020年12月30日 17:12'
            delSpeakP(element)
            i=i+1
            # 生成一个3到7之间的随机浮点数
            delay_time = random.uniform(0, 1)

            # 打印随机延迟的时间
            print(f"Delaying for {delay_time:.2f} seconds")

            # 暂停程序
            time.sleep(delay_time)
    return response








#---------------------------------


def get_message_myPhone(start, count):

    # 定义查询参数字典
    params = {
        'qzonetoken': '8b38c27bff4be3e00d65c4060a719050c8289c879d6ba05ccbdc64440b90dca402bc17cdeba23fc6e65c2d7acf6c8c294669a8a1f05bb8dc377382abad9854fb02043c89f5627e52d7c3',
        'g_tk': uin,
        'res_type': '1',
        'res_attach': 'att=back_server_info=offset%3D'+str(start)+'%26total%3D'+str(count)+'%26basetime%3D1598312771%26feedsource%3D1&lastrefreshtime=1733904752&lastseparatortime=0&loadcount=0',
        'tl': '1598312771',
        'refresh_type': '2',
        'format': 'json'
    }

    response = requests.get('https://mobile.qzone.qq.com/get_feeds',
                            params=params, cookies=cookies, headers=headers)

    print(response.text)
    html = Tools.process_old_html(response.text)

    soup = BeautifulSoup(html, 'html.parser')

    da=soup.find_all('li', class_='f-single f-s-s')
    if len(da)==0:
        print(html)
        print("网络繁忙")
        return 0
    else:


        i = 0
        # 遍历打乱后的列表
        for  element in da:
            tm = element.find_all('span', class_='ui-mr8 state')
            tmm=tm[0].getText()
            print(tmm)
            if '2017' in tmm or '2016' in tmm:
                print("删除",tmm)
                delMy(response.text, i, element)





            # 假设 delMy 已经被正确定义
            i=i+1
            #break
            #continue


        # 如果需要，打印映射以确认元素是否正确存储

